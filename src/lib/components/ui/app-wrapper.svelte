<!-- LayoutWrapper.svelte -->
<script lang="ts">
	import type { Snippet } from 'svelte';

	interface HeaderConfig {
		show: boolean;
		position: 'default' | 'adaptable' | 'fixed';
	}

	interface FooterConfig {
		show: boolean;
		position: 'default' | 'adaptable' | 'fixed';
	}

	interface Props {
		mode: 'normal' | 'sidebar';
		header?: HeaderConfig;
		footer?: FooterConfig;
		breakpoint?: number;
		// Snippets
		headerSnippet?: Snippet;
		sidebarSnippet?: Snippet;
		footerSnippet?: Snippet;
		children?: Snippet;
	}

	let {
		mode = 'normal',
		header = { show: true, position: 'default' },
		footer = { show: true, position: 'default' },
		breakpoint = 1024,
		headerSnippet,
		sidebarSnippet,
		footerSnippet,
		children
	}: Props = $props();

	// Computed classes based on configuration
	const layoutClasses = $derived(
		[
			'layout-wrapper',
			`layout-wrapper--${mode}`,
			header.show && header.position !== 'default' && 'layout-wrapper--header-' + header.position,
			footer.show && footer.position !== 'default' && 'layout-wrapper--footer-' + footer.position
		]
			.filter(Boolean)
			.join(' ')
	);

	const cssVariables = $derived(`--breakpoint: ${breakpoint}px;`);
</script>

<div class={layoutClasses} style={cssVariables}>
	{#if mode === 'normal'}
		<!-- NORMAL LAYOUT: Header + Content + Footer -->
		<div class="normal-layout">
			{#if header.show && headerSnippet}
				<header class="header-wrapper header-wrapper--{header.position}">
					{@render headerSnippet()}
				</header>
			{/if}

			<main class="content-wrapper">
				{@render children?.()}
			</main>

			{#if footer.show && footerSnippet}
				<footer class="footer-wrapper footer-wrapper--{footer.position}">
					{@render footerSnippet()}
				</footer>
			{/if}
		</div>
	{:else if mode === 'sidebar'}
		<!-- SIDEBAR LAYOUT: Header + Sidebar + Content + Footer -->
		<div class="sidebar-layout">
			{#if header.show && headerSnippet}
				<header class="header-wrapper header-wrapper--{header.position}">
					{@render headerSnippet()}
				</header>
			{/if}

			<aside class="sidebar-wrapper">
				{@render sidebarSnippet?.()}
			</aside>

			<main class="content-wrapper">
				{@render children?.()}
			</main>

			{#if footer.show && footerSnippet}
				<footer class="footer-wrapper footer-wrapper--{footer.position}">
					{@render footerSnippet()}
				</footer>
			{/if}
		</div>
	{/if}
</div>

<style>
	/* ==========================================================================
     CORE LAYOUT STYLES
     ========================================================================== */

	.layout-wrapper {
		width: 100%;
		height: 100vh;
		height: 100dvh; /* Modern viewport height */
		display: flex;
		flex-direction: column;
	}

	/* ==========================================================================
     NORMAL LAYOUT (Header + Content + Footer)
     ========================================================================== */

	.normal-layout {
		display: grid;
		grid-template-rows: auto 1fr auto;
		height: 100%;
		min-height: 0; /* Prevent flex/grid overflow issues */
	}

	/* ==========================================================================
     SIDEBAR LAYOUT (Header + Sidebar + Content + Footer)
     ========================================================================== */

	.sidebar-layout {
		display: grid;
		grid-template-areas:
			'header header'
			'sidebar content'
			'footer footer';
		grid-template-columns: auto 1fr;
		grid-template-rows: auto 1fr auto;
		height: 100%;
		min-height: 0;
	}

	/* Hide header row if no header */
	.layout-wrapper--sidebar:not(.layout-wrapper--header-default) .sidebar-layout {
		grid-template-areas:
			'sidebar content'
			'footer footer';
		grid-template-rows: 1fr auto;
	}

	/* Hide footer row if no footer */
	.layout-wrapper--sidebar:not(.layout-wrapper--footer-default) .sidebar-layout {
		grid-template-areas:
			'header header'
			'sidebar content';
		grid-template-rows: auto 1fr;
	}

	/* Hide both header and footer */
	.layout-wrapper--sidebar:not(.layout-wrapper--header-default):not(.layout-wrapper--footer-default)
		.sidebar-layout {
		grid-template-areas: 'sidebar content';
		grid-template-rows: 1fr;
	}

	/* ==========================================================================
     COMPONENT WRAPPERS
     ========================================================================== */

	.header-wrapper {
		grid-area: header;
		z-index: 100;
	}

	.sidebar-wrapper {
		grid-area: sidebar;
		min-width: 0;
		overflow-y: auto;
		overflow-x: hidden;
		z-index: 50;
	}

	.content-wrapper {
		grid-area: content;
		min-width: 0;
		min-height: 0;
		overflow-y: auto;
		overflow-x: hidden;
		z-index: 10;
	}

	.footer-wrapper {
		grid-area: footer;
		z-index: 100;
	}

	/* ==========================================================================
     HEADER POSITIONS
     ========================================================================== */

	.header-wrapper--default {
		position: relative;
	}

	.header-wrapper--fixed {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		backdrop-filter: blur(8px);
		background: rgba(255, 255, 255, 0.95);
		border-bottom: 1px solid rgba(0, 0, 0, 0.1);
	}

	.header-wrapper--adaptable {
		position: relative;
	}

	/* ==========================================================================
     FOOTER POSITIONS
     ========================================================================== */

	.footer-wrapper--default {
		position: relative;
	}

	.footer-wrapper--fixed {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		backdrop-filter: blur(8px);
		background: rgba(255, 255, 255, 0.95);
		border-top: 1px solid rgba(0, 0, 0, 0.1);
	}

	.footer-wrapper--adaptable {
		position: relative;
	}

	/* ==========================================================================
     RESPONSIVE BEHAVIOR
     ========================================================================== */

	@media (max-width: 1024px) {
		/* MOBILE TRANSFORMATIONS */

		/* Sidebar becomes overlay or stacked */
		.sidebar-layout {
			grid-template-areas:
				'header'
				'content'
				'footer';
			grid-template-columns: 1fr;
			grid-template-rows: auto 1fr auto;
		}

		.sidebar-wrapper {
			position: fixed;
			top: 0;
			left: 0;
			bottom: 0;
			width: 280px;
			background: white;
			box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
			transform: translateX(-100%);
			transition: transform 0.3s ease;
			z-index: 200;
		}

		/* Show sidebar when active (you'd control this with a state) */
		.sidebar-wrapper.sidebar-wrapper--active {
			transform: translateX(0);
		}

		/* Adaptable header becomes fixed on mobile */
		.header-wrapper--adaptable {
			position: fixed;
			top: 0;
			left: 0;
			right: 0;
			backdrop-filter: blur(8px);
			background: rgba(255, 255, 255, 0.95);
			border-bottom: 1px solid rgba(0, 0, 0, 0.1);
			z-index: 150;
		}

		/* Adaptable footer becomes fixed on mobile */
		.footer-wrapper--adaptable {
			position: fixed;
			bottom: 0;
			left: 0;
			right: 0;
			backdrop-filter: blur(8px);
			background: rgba(255, 255, 255, 0.95);
			border-top: 1px solid rgba(0, 0, 0, 0.1);
			z-index: 150;
		}

		/* Adjust content padding when fixed header/footer */
		.layout-wrapper--header-adaptable .content-wrapper,
		.layout-wrapper--header-fixed .content-wrapper {
			padding-top: var(--header-height, 60px);
		}

		.layout-wrapper--footer-adaptable .content-wrapper,
		.layout-wrapper--footer-fixed .content-wrapper {
			padding-bottom: var(--footer-height, 60px);
		}
	}

	/* ==========================================================================
     SCROLL BEHAVIOR FIXES
     ========================================================================== */

	/* Prevent scroll issues on iOS */
	.content-wrapper {
		-webkit-overflow-scrolling: touch;
		overscroll-behavior: contain;
	}

	.sidebar-wrapper {
		-webkit-overflow-scrolling: touch;
		overscroll-behavior: contain;
	}

	/* ==========================================================================
     UTILITY CLASSES
     ========================================================================== */

	/* Hide scrollbar but keep functionality */
	.content-wrapper::-webkit-scrollbar,
	.sidebar-wrapper::-webkit-scrollbar {
		width: 0px;
		background: transparent;
	}

	/* Focus states for accessibility */
	.header-wrapper:focus-within,
	.footer-wrapper:focus-within {
		outline: 2px solid #3b82f6;
		outline-offset: -2px;
	}

	/* ==========================================================================
     CSS CUSTOM PROPERTIES FOR THEMING
     ========================================================================== */

	.layout-wrapper {
		--header-height: 60px;
		--footer-height: 60px;
		--sidebar-width: 280px;
		--content-padding: 1rem;
		--border-color: rgba(0, 0, 0, 0.1);
		--backdrop-blur: blur(8px);
		--backdrop-bg: rgba(255, 255, 255, 0.95);
		--shadow-overlay: 2px 0 8px rgba(0, 0, 0, 0.15);
		--transition-speed: 0.3s;
		--z-header: 100;
		--z-footer: 100;
		--z-sidebar: 50;
		--z-content: 10;
		--z-mobile-overlay: 200;
	}

	/* Dark mode support */
	@media (prefers-color-scheme: dark) {
		.layout-wrapper {
			--backdrop-bg: rgba(0, 0, 0, 0.95);
			--border-color: rgba(255, 255, 255, 0.1);
		}
	}
</style>
