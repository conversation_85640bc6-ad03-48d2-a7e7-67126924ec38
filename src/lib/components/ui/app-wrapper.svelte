<!-- LayoutWrapper.svelte -->
<script lang="ts">
	import type { Snippet } from 'svelte';

	interface HeaderConfig {
		show: boolean;
		position: 'default' | 'adaptable' | 'fixed';
	}

	interface FooterConfig {
		show: boolean;
		position: 'default' | 'adaptable' | 'fixed';
	}

	interface Props {
		mode: 'normal' | 'sidebar';
		header?: HeaderConfig;
		footer?: FooterConfig;
		breakpoint?: number;
		// Snippets
		headerSnippet?: Snippet;
		sidebarSnippet?: Snippet;
		footerSnippet?: Snippet;
		children?: Snippet;
	}

	let {
		mode = 'normal',
		header = { show: true, position: 'default' },
		footer = { show: true, position: 'default' },
		breakpoint = 1024,
		headerSnippet,
		sidebarSnippet,
		footerSnippet,
		children
	}: Props = $props();

	const hasHeader = $derived(header.show && headerSnippet);
	const hasFooter = $derived(footer.show && footerSnippet);
	const hasSidebar = $derived(mode === 'sidebar' && sidebarSnippet);
</script>

<div
	class="layout-wrapper"
	class:layout-wrapper--normal={mode === 'normal'}
	class:layout-wrapper--sidebar={mode === 'sidebar'}
	class:layout-wrapper--has-header={hasHeader}
	class:layout-wrapper--has-footer={hasFooter}
	class:layout-wrapper--header-fixed={hasHeader && header.position === 'fixed'}
	class:layout-wrapper--header-adaptable={hasHeader && header.position === 'adaptable'}
	class:layout-wrapper--footer-fixed={hasFooter && footer.position === 'fixed'}
	class:layout-wrapper--footer-adaptable={hasFooter && footer.position === 'adaptable'}
	style="--breakpoint: {breakpoint}px;"
>
	{#if mode === 'normal'}
		<!-- NORMAL LAYOUT -->
		{#if hasHeader}
			<header class="header-wrapper header-wrapper--{header.position}">
				{@render headerSnippet?.()}
			</header>
		{/if}

		<main class="content-wrapper">
			{@render children?.()}
		</main>

		{#if hasFooter}
			<footer class="footer-wrapper footer-wrapper--{footer.position}">
				{@render footerSnippet?.()}
			</footer>
		{/if}
	{:else}
		<!-- SIDEBAR LAYOUT -->
		{#if hasHeader}
			<header class="header-wrapper header-wrapper--{header.position}">
				{@render headerSnippet?.()}
			</header>
		{/if}

		{#if hasSidebar}
			<aside class="sidebar-wrapper">
				{@render sidebarSnippet?.()}
			</aside>
		{/if}

		<main class="content-wrapper">
			{@render children?.()}
		</main>

		{#if hasFooter}
			<footer class="footer-wrapper footer-wrapper--{footer.position}">
				{@render footerSnippet?.()}
			</footer>
		{/if}
	{/if}
</div>

<style>
	.layout-wrapper {
		width: 100%;
		height: 100vh;
		display: grid;
		overflow: hidden;
	}

	.layout-wrapper--normal {
		grid-template-rows: auto 1fr auto;
		grid-template-areas:
			'header'
			'content'
			'footer';
	}

	.layout-wrapper--normal:not(.layout-wrapper--has-header) {
		grid-template-rows: 1fr auto;
		grid-template-areas:
			'content'
			'footer';
	}

	.layout-wrapper--normal:not(.layout-wrapper--has-footer) {
		grid-template-rows: auto 1fr;
		grid-template-areas:
			'header'
			'content';
	}

	.layout-wrapper--normal:not(.layout-wrapper--has-header):not(.layout-wrapper--has-footer) {
		grid-template-rows: 1fr;
		grid-template-areas: 'content';
	}

	.layout-wrapper--sidebar {
		grid-template-columns: auto 1fr;
		grid-template-rows: auto 1fr auto;
		grid-template-areas:
			'header header'
			'sidebar content'
			'footer footer';
	}

	.layout-wrapper--sidebar:not(.layout-wrapper--has-header) {
		grid-template-rows: 1fr auto;
		grid-template-areas:
			'sidebar content'
			'footer footer';
	}

	.layout-wrapper--sidebar:not(.layout-wrapper--has-footer) {
		grid-template-rows: auto 1fr;
		grid-template-areas:
			'header header'
			'sidebar content';
	}

	.layout-wrapper--sidebar:not(.layout-wrapper--has-header):not(.layout-wrapper--has-footer) {
		grid-template-rows: 1fr;
		grid-template-areas: 'sidebar content';
	}

	.header-wrapper {
		grid-area: header;
	}
	.sidebar-wrapper {
		grid-area: sidebar;
	}
	.content-wrapper {
		grid-area: content;
		overflow-y: auto;
		min-height: 0;
	}
	.footer-wrapper {
		grid-area: footer;
	}

	.header-wrapper--default {
		position: relative;
	}

	.header-wrapper--fixed {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		backdrop-filter: blur(8px);
		background: rgba(255, 255, 255, 0.95);
		border-bottom: 1px solid rgba(0, 0, 0, 0.1);
	}

	.header-wrapper--adaptable {
		position: relative;
	}

	.footer-wrapper--default {
		position: relative;
	}

	.footer-wrapper--fixed {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		backdrop-filter: blur(8px);
		background: rgba(255, 255, 255, 0.95);
		border-top: 1px solid rgba(0, 0, 0, 0.1);
	}

	.footer-wrapper--adaptable {
		position: relative;
	}

	@media (max-width: 1024px) {
		/* MOBILE TRANSFORMATIONS */

		/* Sidebar becomes overlay or stacked */
		.sidebar-layout {
			grid-template-areas:
				'header'
				'content'
				'footer';
			grid-template-columns: 1fr;
			grid-template-rows: auto 1fr auto;
		}

		.sidebar-wrapper {
			position: fixed;
			top: 0;
			left: 0;
			bottom: 0;
			width: 280px;
			background: white;
			box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
			transform: translateX(-100%);
			transition: transform 0.3s ease;
			z-index: 200;
		}

		/* Show sidebar when active (you'd control this with a state) */
		.sidebar-wrapper.sidebar-wrapper--active {
			transform: translateX(0);
		}

		/* Adaptable header becomes fixed on mobile */
		.header-wrapper--adaptable {
			position: fixed;
			top: 0;
			left: 0;
			right: 0;
			backdrop-filter: blur(8px);
			background: rgba(255, 255, 255, 0.95);
			border-bottom: 1px solid rgba(0, 0, 0, 0.1);
			z-index: 150;
		}

		/* Adaptable footer becomes fixed on mobile */
		.footer-wrapper--adaptable {
			position: fixed;
			bottom: 0;
			left: 0;
			right: 0;
			backdrop-filter: blur(8px);
			background: rgba(255, 255, 255, 0.95);
			border-top: 1px solid rgba(0, 0, 0, 0.1);
			z-index: 150;
		}

		/* Adjust content padding when fixed header/footer */
		.layout-wrapper--header-adaptable .content-wrapper,
		.layout-wrapper--header-fixed .content-wrapper {
			padding-top: var(--header-height, 60px);
		}

		.layout-wrapper--footer-adaptable .content-wrapper,
		.layout-wrapper--footer-fixed .content-wrapper {
			padding-bottom: var(--footer-height, 60px);
		}
	}

	/* Prevent scroll issues on iOS */
	.content-wrapper {
		-webkit-overflow-scrolling: touch;
		overscroll-behavior: contain;
	}

	.sidebar-wrapper {
		-webkit-overflow-scrolling: touch;
		overscroll-behavior: contain;
	}

	/* ==========================================================================
     UTILITY CLASSES
     ========================================================================== */

	/* Hide scrollbar but keep functionality */
	.content-wrapper::-webkit-scrollbar,
	.sidebar-wrapper::-webkit-scrollbar {
		width: 0px;
		background: transparent;
	}

	/* Focus states for accessibility */
	.header-wrapper:focus-within,
	.footer-wrapper:focus-within {
		outline: 2px solid #3b82f6;
		outline-offset: -2px;
	}

	/* ==========================================================================
     CSS CUSTOM PROPERTIES FOR THEMING
     ========================================================================== */

	.layout-wrapper {
		--header-height: 60px;
		--footer-height: 60px;
		--sidebar-width: 280px;
		--content-padding: 1rem;
		--border-color: rgba(0, 0, 0, 0.1);
		--backdrop-blur: blur(8px);
		--backdrop-bg: rgba(255, 255, 255, 0.95);
		--shadow-overlay: 2px 0 8px rgba(0, 0, 0, 0.15);
		--transition-speed: 0.3s;
		--z-header: 100;
		--z-footer: 100;
		--z-sidebar: 50;
		--z-content: 10;
		--z-mobile-overlay: 200;
	}

	/* Dark mode support */
	@media (prefers-color-scheme: dark) {
		.layout-wrapper {
			--backdrop-bg: rgba(0, 0, 0, 0.95);
			--border-color: rgba(255, 255, 255, 0.1);
		}
	}
</style>
