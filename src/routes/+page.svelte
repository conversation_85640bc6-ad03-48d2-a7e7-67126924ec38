<script lang="ts">
	import AppWrapper from '$lib/components/ui/app-wrapper.svelte';
</script>

<AppWrapper
	mode="normal"
	header={{ show: true, position: 'adaptable' }}
	footer={{ show: true, position: 'default' }}
>
	{#snippet headerSnippet()}
		<!-- HEADER/NAVBAR -->
		<nav class="border-b border-gray-200 bg-white/95 px-4 backdrop-blur-sm sm:px-6 lg:px-8">
			<div class="mx-auto max-w-7xl">
				<div class="flex h-16 items-center justify-between">
					<!-- Logo -->
					<div class="flex items-center">
						<div class="flex-shrink-0">
							<h1 class="text-2xl font-bold text-gray-900">MyApp</h1>
						</div>
					</div>

					<!-- Desktop Navigation -->
					<div class="hidden md:block">
						<div class="ml-10 flex items-baseline space-x-4">
							<a
								href="#features"
								class="rounded-md px-3 py-2 text-sm font-medium text-gray-600 transition-colors hover:text-gray-900"
								>Features</a
							>
							<a
								href="#pricing"
								class="rounded-md px-3 py-2 text-sm font-medium text-gray-600 transition-colors hover:text-gray-900"
								>Pricing</a
							>
							<a
								href="#about"
								class="rounded-md px-3 py-2 text-sm font-medium text-gray-600 transition-colors hover:text-gray-900"
								>About</a
							>
							<a
								href="#contact"
								class="rounded-md px-3 py-2 text-sm font-medium text-gray-600 transition-colors hover:text-gray-900"
								>Contact</a
							>
						</div>
					</div>

					<!-- CTA Buttons -->
					<div class="hidden items-center space-x-4 md:flex">
						<button
							class="rounded-md px-3 py-2 text-sm font-medium text-gray-600 transition-colors hover:text-gray-900"
						>
							Sign In
						</button>
						<button
							class="rounded-lg bg-blue-600 px-4 py-2 text-sm font-medium text-white transition-colors hover:bg-blue-700"
						>
							Get Started
						</button>
					</div>

					<!-- Mobile menu button -->
					<div class="md:hidden">
						<button class="p-2 text-gray-600 hover:text-gray-900">
							<svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M4 6h16M4 12h16M4 18h16"
								/>
							</svg>
						</button>
					</div>
				</div>
			</div>
		</nav>
	{/snippet}

	{#snippet children()}
		<!-- HERO SECTION -->
		<section
			class="bg-gradient-to-br from-blue-50 via-white to-purple-50 px-4 pt-20 pb-16 sm:px-6 lg:px-8"
		>
			<div class="mx-auto max-w-7xl">
				<div class="text-center">
					<h1 class="mb-6 text-4xl font-bold text-gray-900 sm:text-5xl lg:text-6xl">
						Build Something
						<span
							class="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent"
						>
							Amazing
						</span>
					</h1>
					<p class="mx-auto mb-8 max-w-3xl text-xl text-gray-600">
						The modern platform that helps you create, deploy, and scale your applications with
						ease. Join thousands of developers who trust our solution.
					</p>
					<div class="flex flex-col justify-center gap-4 sm:flex-row">
						<button
							class="transform rounded-lg bg-blue-600 px-8 py-4 text-lg font-semibold text-white shadow-lg transition-all hover:scale-105 hover:bg-blue-700"
						>
							Start Free Trial
						</button>
						<button
							class="rounded-lg border-2 border-gray-300 px-8 py-4 text-lg font-semibold text-gray-700 transition-all hover:border-gray-400 hover:bg-gray-50"
						>
							Watch Demo
						</button>
					</div>
				</div>

				<!-- Hero Image/Mockup -->
				<div class="mt-16">
					<div class="mx-auto max-w-4xl rounded-2xl bg-white p-8 shadow-2xl">
						<div class="flex h-64 items-center justify-center rounded-lg bg-gray-100 sm:h-80">
							<div class="text-center">
								<div
									class="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-blue-600"
								>
									<svg
										class="h-8 w-8 text-white"
										fill="none"
										viewBox="0 0 24 24"
										stroke="currentColor"
									>
										<path
											stroke-linecap="round"
											stroke-linejoin="round"
											stroke-width="2"
											d="M13 10V3L4 14h7v7l9-11h-7z"
										/>
									</svg>
								</div>
								<p class="text-gray-600">Your App Preview</p>
							</div>
						</div>
					</div>
				</div>
			</div>
		</section>

		<!-- FEATURES SECTION -->
		<section id="features" class="bg-white px-4 py-20 sm:px-6 lg:px-8">
			<div class="mx-auto max-w-7xl">
				<div class="mb-16 text-center">
					<h2 class="mb-4 text-3xl font-bold text-gray-900 sm:text-4xl">
						Everything you need to succeed
					</h2>
					<p class="mx-auto max-w-2xl text-xl text-gray-600">
						Powerful features designed to help you build, deploy, and scale your applications faster
						than ever.
					</p>
				</div>

				<div class="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
					<!-- Feature 1 -->
					<div class="rounded-xl bg-gray-50 p-8 transition-shadow hover:shadow-lg">
						<div class="mb-6 flex h-12 w-12 items-center justify-center rounded-lg bg-blue-600">
							<svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M13 10V3L4 14h7v7l9-11h-7z"
								/>
							</svg>
						</div>
						<h3 class="mb-4 text-xl font-semibold text-gray-900">Lightning Fast</h3>
						<p class="text-gray-600">
							Built for speed with modern technologies. Deploy in seconds, not minutes.
						</p>
					</div>

					<!-- Feature 2 -->
					<div class="rounded-xl bg-gray-50 p-8 transition-shadow hover:shadow-lg">
						<div class="mb-6 flex h-12 w-12 items-center justify-center rounded-lg bg-green-600">
							<svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
								/>
							</svg>
						</div>
						<h3 class="mb-4 text-xl font-semibold text-gray-900">Secure by Default</h3>
						<p class="text-gray-600">
							Enterprise-grade security with automatic updates and monitoring.
						</p>
					</div>

					<!-- Feature 3 -->
					<div class="rounded-xl bg-gray-50 p-8 transition-shadow hover:shadow-lg">
						<div class="mb-6 flex h-12 w-12 items-center justify-center rounded-lg bg-purple-600">
							<svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
								/>
							</svg>
						</div>
						<h3 class="mb-4 text-xl font-semibold text-gray-900">Developer Friendly</h3>
						<p class="text-gray-600">
							Intuitive APIs and comprehensive documentation to get you started quickly.
						</p>
					</div>
				</div>
			</div>
		</section>

		<!-- PRICING SECTION -->
		<section id="pricing" class="bg-gray-50 px-4 py-20 sm:px-6 lg:px-8">
			<div class="mx-auto max-w-7xl">
				<div class="mb-16 text-center">
					<h2 class="mb-4 text-3xl font-bold text-gray-900 sm:text-4xl">
						Simple, transparent pricing
					</h2>
					<p class="text-xl text-gray-600">Choose the plan that's right for you</p>
				</div>

				<div class="mx-auto grid max-w-5xl grid-cols-1 gap-8 lg:grid-cols-3">
					<!-- Starter Plan -->
					<div class="rounded-2xl bg-white p-8 shadow-lg">
						<h3 class="mb-4 text-2xl font-bold text-gray-900">Starter</h3>
						<div class="mb-6">
							<span class="text-4xl font-bold text-gray-900">$9</span>
							<span class="text-gray-600">/month</span>
						</div>
						<ul class="mb-8 space-y-4">
							<li class="flex items-center">
								<svg
									class="mr-3 h-5 w-5 text-green-500"
									fill="none"
									viewBox="0 0 24 24"
									stroke="currentColor"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										stroke-width="2"
										d="M5 13l4 4L19 7"
									/>
								</svg>
								Up to 5 projects
							</li>
							<li class="flex items-center">
								<svg
									class="mr-3 h-5 w-5 text-green-500"
									fill="none"
									viewBox="0 0 24 24"
									stroke="currentColor"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										stroke-width="2"
										d="M5 13l4 4L19 7"
									/>
								</svg>
								10GB storage
							</li>
							<li class="flex items-center">
								<svg
									class="mr-3 h-5 w-5 text-green-500"
									fill="none"
									viewBox="0 0 24 24"
									stroke="currentColor"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										stroke-width="2"
										d="M5 13l4 4L19 7"
									/>
								</svg>
								Email support
							</li>
						</ul>
						<button
							class="w-full rounded-lg bg-gray-900 py-3 font-semibold text-white transition-colors hover:bg-gray-800"
						>
							Get Started
						</button>
					</div>

					<!-- Pro Plan (Featured) -->
					<div class="scale-105 transform rounded-2xl bg-blue-600 p-8 shadow-xl">
						<div class="mb-4 text-center">
							<span class="rounded-full bg-white px-3 py-1 text-sm font-semibold text-blue-600"
								>Most Popular</span
							>
						</div>
						<h3 class="mb-4 text-2xl font-bold text-white">Pro</h3>
						<div class="mb-6">
							<span class="text-4xl font-bold text-white">$29</span>
							<span class="text-blue-100">/month</span>
						</div>
						<ul class="mb-8 space-y-4 text-white">
							<li class="flex items-center">
								<svg
									class="mr-3 h-5 w-5 text-blue-200"
									fill="none"
									viewBox="0 0 24 24"
									stroke="currentColor"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										stroke-width="2"
										d="M5 13l4 4L19 7"
									/>
								</svg>
								Unlimited projects
							</li>
							<li class="flex items-center">
								<svg
									class="mr-3 h-5 w-5 text-blue-200"
									fill="none"
									viewBox="0 0 24 24"
									stroke="currentColor"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										stroke-width="2"
										d="M5 13l4 4L19 7"
									/>
								</svg>
								100GB storage
							</li>
							<li class="flex items-center">
								<svg
									class="mr-3 h-5 w-5 text-blue-200"
									fill="none"
									viewBox="0 0 24 24"
									stroke="currentColor"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										stroke-width="2"
										d="M5 13l4 4L19 7"
									/>
								</svg>
								Priority support
							</li>
						</ul>
						<button
							class="w-full rounded-lg bg-white py-3 font-semibold text-blue-600 transition-colors hover:bg-gray-50"
						>
							Start Free Trial
						</button>
					</div>

					<!-- Enterprise Plan -->
					<div class="rounded-2xl bg-white p-8 shadow-lg">
						<h3 class="mb-4 text-2xl font-bold text-gray-900">Enterprise</h3>
						<div class="mb-6">
							<span class="text-4xl font-bold text-gray-900">$99</span>
							<span class="text-gray-600">/month</span>
						</div>
						<ul class="mb-8 space-y-4">
							<li class="flex items-center">
								<svg
									class="mr-3 h-5 w-5 text-green-500"
									fill="none"
									viewBox="0 0 24 24"
									stroke="currentColor"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										stroke-width="2"
										d="M5 13l4 4L19 7"
									/>
								</svg>
								Everything in Pro
							</li>
							<li class="flex items-center">
								<svg
									class="mr-3 h-5 w-5 text-green-500"
									fill="none"
									viewBox="0 0 24 24"
									stroke="currentColor"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										stroke-width="2"
										d="M5 13l4 4L19 7"
									/>
								</svg>
								1TB storage
							</li>
							<li class="flex items-center">
								<svg
									class="mr-3 h-5 w-5 text-green-500"
									fill="none"
									viewBox="0 0 24 24"
									stroke="currentColor"
								>
									<path
										stroke-linecap="round"
										stroke-linejoin="round"
										stroke-width="2"
										d="M5 13l4 4L19 7"
									/>
								</svg>
								24/7 phone support
							</li>
						</ul>
						<button
							class="w-full rounded-lg bg-gray-900 py-3 font-semibold text-white transition-colors hover:bg-gray-800"
						>
							Contact Sales
						</button>
					</div>
				</div>
			</div>
		</section>

		<!-- CTA SECTION -->
		<section class="bg-gray-900 px-4 py-20 sm:px-6 lg:px-8">
			<div class="mx-auto max-w-4xl text-center">
				<h2 class="mb-6 text-3xl font-bold text-white sm:text-4xl">Ready to get started?</h2>
				<p class="mb-8 text-xl text-gray-300">
					Join thousands of developers who are already building amazing applications with our
					platform.
				</p>
				<div class="flex flex-col justify-center gap-4 sm:flex-row">
					<button
						class="transform rounded-lg bg-blue-600 px-8 py-4 text-lg font-semibold text-white transition-all hover:scale-105 hover:bg-blue-700"
					>
						Start Your Free Trial
					</button>
					<button
						class="rounded-lg border-2 border-gray-600 px-8 py-4 text-lg font-semibold text-gray-300 transition-all hover:border-gray-500 hover:text-white"
					>
						Talk to Sales
					</button>
				</div>
			</div>
		</section>
	{/snippet}

	{#snippet footerSnippet()}
		<!-- FOOTER -->
		<footer class="border-t border-gray-200 bg-gray-50 px-4 py-12 sm:px-6 lg:px-8">
			<div class="mx-auto max-w-7xl">
				<div class="grid grid-cols-1 gap-8 md:grid-cols-4">
					<!-- Company Info -->
					<div class="col-span-1 md:col-span-2">
						<h3 class="mb-4 text-2xl font-bold text-gray-900">MyApp</h3>
						<p class="mb-6 max-w-md text-gray-600">
							Building the future of application development with modern tools and technologies.
						</p>
						<div class="flex space-x-4">
							<a href="#" class="text-gray-400 transition-colors hover:text-gray-600">
								<svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
									<path
										d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"
									/>
								</svg>
							</a>
							<a href="#" class="text-gray-400 transition-colors hover:text-gray-600">
								<svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
									<path
										d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z"
									/>
								</svg>
							</a>
							<a href="#" class="text-gray-400 transition-colors hover:text-gray-600">
								<svg class="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
									<path
										d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"
									/>
								</svg>
							</a>
						</div>
					</div>

					<!-- Quick Links -->
					<div>
						<h4 class="mb-4 text-lg font-semibold text-gray-900">Product</h4>
						<ul class="space-y-3">
							<li>
								<a href="#" class="text-gray-600 transition-colors hover:text-gray-900">Features</a>
							</li>
							<li>
								<a href="#" class="text-gray-600 transition-colors hover:text-gray-900">Pricing</a>
							</li>
							<li>
								<a href="#" class="text-gray-600 transition-colors hover:text-gray-900"
									>Documentation</a
								>
							</li>
							<li>
								<a href="#" class="text-gray-600 transition-colors hover:text-gray-900"
									>API Reference</a
								>
							</li>
						</ul>
					</div>

					<!-- Support -->
					<div>
						<h4 class="mb-4 text-lg font-semibold text-gray-900">Support</h4>
						<ul class="space-y-3">
							<li>
								<a href="#" class="text-gray-600 transition-colors hover:text-gray-900"
									>Help Center</a
								>
							</li>
							<li>
								<a href="#" class="text-gray-600 transition-colors hover:text-gray-900"
									>Contact Us</a
								>
							</li>
							<li>
								<a href="#" class="text-gray-600 transition-colors hover:text-gray-900">Status</a>
							</li>
							<li>
								<a href="#" class="text-gray-600 transition-colors hover:text-gray-900">Community</a
								>
							</li>
						</ul>
					</div>
				</div>

				<!-- Bottom Footer -->
				<div class="mt-12 border-t border-gray-200 pt-8">
					<div class="flex flex-col items-center justify-between md:flex-row">
						<p class="text-sm text-gray-600">© 2024 MyApp. All rights reserved.</p>
						<div class="mt-4 flex space-x-6 md:mt-0">
							<a href="#" class="text-sm text-gray-600 transition-colors hover:text-gray-900"
								>Privacy Policy</a
							>
							<a href="#" class="text-sm text-gray-600 transition-colors hover:text-gray-900"
								>Terms of Service</a
							>
							<a href="#" class="text-sm text-gray-600 transition-colors hover:text-gray-900"
								>Cookie Policy</a
							>
						</div>
					</div>
				</div>
			</div>
		</footer>
	{/snippet}
</AppWrapper>
